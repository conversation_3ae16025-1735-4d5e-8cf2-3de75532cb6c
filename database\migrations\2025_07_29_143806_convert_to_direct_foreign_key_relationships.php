<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop pivot tables if they exist
        Schema::dropIfExists('article_blog_tag');
        Schema::dropIfExists('portfolio_case_portfolio_tag');

        // Add foreign key columns to articles table if not exists
        if (!Schema::hasColumn('articles', 'blog_tag_id')) {
            Schema::table('articles', function (Blueprint $table) {
                $table->foreignId('blog_tag_id')->nullable()->constrained()->onDelete('cascade');
            });
        }

        // Remove old tag column from articles if it exists
        if (Schema::hasColumn('articles', 'tag')) {
            Schema::table('articles', function (Blueprint $table) {
                $table->dropColumn('tag');
            });
        }

        // Add foreign key column to cases table if not exists
        if (!Schema::hasColumn('cases', 'portfolio_tag_id')) {
            Schema::table('cases', function (Blueprint $table) {
                $table->foreignId('portfolio_tag_id')->nullable()->constrained()->onDelete('cascade');
            });
        }

        // Remove old tags column from cases if it exists
        if (Schema::hasColumn('cases', 'tags')) {
            Schema::table('cases', function (Blueprint $table) {
                $table->dropColumn('tags');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back old columns
        Schema::table('articles', function (Blueprint $table) {
            $table->string('tag')->nullable();
            $table->dropForeign(['blog_tag_id']);
            $table->dropColumn('blog_tag_id');
        });

        Schema::table('cases', function (Blueprint $table) {
            $table->json('tags')->nullable();
            $table->dropForeign(['portfolio_tag_id']);
            $table->dropColumn('portfolio_tag_id');
        });

        // Recreate pivot tables
        Schema::create('article_blog_tag', function (Blueprint $table) {
            $table->id();
            $table->foreignId('article_id')->constrained()->onDelete('cascade');
            $table->foreignId('blog_tag_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            $table->unique(['article_id', 'blog_tag_id']);
        });

        Schema::create('portfolio_case_portfolio_tag', function (Blueprint $table) {
            $table->id();
            $table->foreignId('portfolio_case_id')->constrained('cases')->onDelete('cascade');
            $table->foreignId('portfolio_tag_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            $table->unique(['portfolio_case_id', 'portfolio_tag_id'], 'case_tag_unique');
        });
    }
};
