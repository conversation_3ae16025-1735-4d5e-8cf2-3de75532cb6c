<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Article;
use App\Models\PortfolioCase;
use App\Models\PortfolioTag;
use App\Models\BlogTag;
use App\Models\ContactQuery;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing tags
        $webDevTag = PortfolioTag::where('name', 'Web Development')->first();
        $uxDesignBlogTag = BlogTag::where('name', 'UX Design')->first();

        // Create test article
        Article::create([
            'slug' => 'test-article',
            'title' => 'Test Article for Admin Panel',
            'blog_tag_id' => $uxDesignBlogTag->id,
            'content' => [
                'This is the first paragraph of our test article.',
                '## Test Heading',
                'This is another paragraph with more content to test the admin panel.',
            ],
            'image' => '/images/placeholder.jpg',
            'date' => now()->format('M d, Y'),
            'read_time' => '3 min read',
        ]);

        // Create test portfolio case
        PortfolioCase::create([
            'slug' => 'test-portfolio-case',
            'title' => 'Test Portfolio Case',
            'company_name' => 'Test Company Ltd.',
            'images' => ['/images/placeholder.jpg', '/images/placeholder.jpg'],
            'thumbnail' => '/images/placeholder.jpg',
            'portfolio_tag_id' => $webDevTag->id,
            'is_published' => true,
        ]);

        // Create test contact query
        ContactQuery::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'company_name' => 'Example Corp',
            'phone_number' => '+****************',
            'message' => 'Hello, I am interested in your web development services. Could you please provide more information about your packages and pricing?',
        ]);

        // Create another contact query
        ContactQuery::create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'company_name' => 'Test Company Inc.',
            'phone_number' => '+****************',
            'message' => 'We are looking for a digital marketing partner to help us grow our online presence. Please contact us to discuss our requirements.',
        ]);
    }
}
