import { Link } from "@inertiajs/react";
import ScrollReveal from "../../components/ScrollReveal";
import { ArrowUpRightIcon } from "@phosphor-icons/react";

const ProjectShowcase = ({ cases = [] }) => (
    <section className="tablet:px-0 bg-white px-2 py-24">
        <ScrollReveal className="mx-auto mb-14 max-w-6xl text-center">
            <h2 className="tablet:text-5xl tablet:font-bold mb-4 text-2xl text-gray-900">
                Building Digital Excellence with Panda.
            </h2>
            <p className="tablet:text-lg mx-auto max-w-2xl text-base text-gray-500">
                Discover the innovative marketing strategies that set Neutra
                apart, driving success in the digital landscape.
            </p>
        </ScrollReveal>
        <div className="desktop:px-0 tablet:grid-cols-2 mx-auto grid w-full max-w-6xl grid-cols-1 gap-8 px-4">
            {cases.map((project, idx) => (
                <Link
                    key={project.title}
                    href={project.url}
                    className="group mb-2 flex flex-col items-center rounded-2xl bg-[#f6f7fa] p-6 shadow-sm transition-all duration-300 hover:scale-[1.025] hover:cursor-pointer focus:ring-2 focus:ring-[#ff6400] focus:ring-offset-2 focus:outline-none"
                    tabIndex={0}
                >
                    <ScrollReveal>
                        <div
                            className="mb-6 flex w-full items-center justify-center overflow-hidden rounded-2xl"
                            style={{
                                background:
                                    idx % 2 === 0
                                        ? "#23402C"
                                        : "linear-gradient(90deg, #6B4EFF 0%, #A259FF 100%)",
                            }}
                        >
                            <img
                                src={project.img}
                                alt={project.title}
                                className="pointer-events-none size-full rounded-[inherit] object-cover object-center transition-transform duration-600 group-hover:scale-110"
                            />
                        </div>
                        <div className="w-full text-left">
                            <div className="mb-3 flex items-center justify-between">
                                <h3 className="pointer-events-none text-xl font-semibold text-gray-900">
                                    {project.title}
                                </h3>
                                <ArrowUpRightIcon
                                    className="tablet:opacity-0 tablet:-translate-x-2 tablet:translate-y-2 tablet:group-hover:opacity-100 tablet:group-hover:translate-x-0 tablet:group-hover:translate-y-0 tablet:transition-all tablet:duration-300 pointer-events-none opacity-100"
                                    size={32}
                                />
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {project.tag && (
                                    <span className="pointer-events-none rounded-full border border-gray-300 bg-white px-4 py-1 text-sm font-medium text-gray-700">
                                        {project.tag}
                                    </span>
                                )}
                            </div>
                        </div>
                    </ScrollReveal>
                </Link>
            ))}
        </div>
    </section>
);

export default ProjectShowcase;
