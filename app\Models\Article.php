<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'image',
        'date',
        'blog_tag_id',
        'title',
        'excerpt',
        'content',
        'writer',
        'read_time',
        'is_featured',
    ];

    protected $casts = [
        'content' => 'array',
        'writer' => 'array',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the blog tag associated with this article.
     */
    public function blogTag()
    {
        return $this->belongsTo(BlogTag::class);
    }
}