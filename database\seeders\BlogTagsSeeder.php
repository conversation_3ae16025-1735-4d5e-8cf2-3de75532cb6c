<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogTag;

class BlogTagsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            [
                'name' => 'UX Design',
                'slug' => 'ux-design',
                'color' => '#3B82F6',
                'description' => 'User Experience design articles',
                'is_active' => true,
            ],
            [
                'name' => 'UI Design',
                'slug' => 'ui-design',
                'color' => '#10B981',
                'description' => 'User Interface design articles',
                'is_active' => true,
            ],
            [
                'name' => 'Web Development',
                'slug' => 'web-development',
                'color' => '#8B5CF6',
                'description' => 'Web development tutorials and tips',
                'is_active' => true,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'color' => '#EC4899',
                'description' => 'Digital marketing strategies and insights',
                'is_active' => true,
            ],
            [
                'name' => 'Technology',
                'slug' => 'technology',
                'color' => '#F59E0B',
                'description' => 'Technology trends and news',
                'is_active' => true,
            ],
        ];

        foreach ($tags as $tagData) {
            BlogTag::create($tagData);
        }
    }
}
