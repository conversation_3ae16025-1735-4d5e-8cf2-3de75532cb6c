import HeroSection from "../sections/Home/HeroSection";
import WhatSetsUsApart from "../sections/Home/WhatSetsUsApart";
import OurServices from "../sections/Home/OurServices";
import ImpactStatement from "../sections/Home/ImpactStatement";
import PricingSection from "../sections/Home/PricingSection";
import TestimonialsSection from "../sections/Home/TestimonialsSection";
import ProjectShowcase from "../sections/Home/ProjectShowcase";
import FaqsSection from "../components/FaqsSection";
import SeoHead from "../components/SeoHead";

const Home = ({ seoData, cases = [] }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <HeroSection />
            <WhatSetsUsApart />
            <OurServices />
            <ImpactStatement />
            <ProjectShowcase cases={cases} />
            <TestimonialsSection />
            <PricingSection />
            <FaqsSection />
        </>
    );
};

export default Home;
