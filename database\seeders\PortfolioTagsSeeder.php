<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PortfolioTag;

class PortfolioTagsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            [
                'name' => 'Web Development',
                'slug' => 'web-development',
                'color' => '#3B82F6',
                'description' => 'Full-stack web development projects',
                'is_active' => true,
            ],
            [
                'name' => 'E-commerce',
                'slug' => 'e-commerce',
                'color' => '#10B981',
                'description' => 'Online store and e-commerce solutions',
                'is_active' => true,
            ],
            [
                'name' => 'Mobile App',
                'slug' => 'mobile-app',
                'color' => '#8B5CF6',
                'description' => 'Mobile application development',
                'is_active' => true,
            ],
            [
                'name' => 'UI/UX Design',
                'slug' => 'ui-ux-design',
                'color' => '#EC4899',
                'description' => 'User interface and user experience design',
                'is_active' => true,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'color' => '#F59E0B',
                'description' => 'Digital marketing campaigns and strategies',
                'is_active' => true,
            ],
            [
                'name' => 'Branding',
                'slug' => 'branding',
                'color' => '#EF4444',
                'description' => 'Brand identity and visual design',
                'is_active' => true,
            ],
        ];

        foreach ($tags as $tagData) {
            PortfolioTag::create($tagData);
        }
    }
}
