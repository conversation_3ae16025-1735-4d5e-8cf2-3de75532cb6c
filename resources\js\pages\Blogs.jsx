import DiscoverSection from "../sections/Blogs/DiscoverSection";
import BlogGridSection from "../sections/Blogs/BlogGridSection";
import SeoHead from "../components/SeoHead";

const Blogs = ({ articles = [], seoData }) => {
    // Get featured articles (same logic as DiscoverSection)
    const featuredArticles = articles
        .filter((article) => article.is_featured)
        .slice(0, 2);

    // Get featured article IDs for exclusion
    const featuredArticleIds = featuredArticles.map((article) => article.id);

    // Filter out featured articles from the grid section
    const nonFeaturedArticles = articles.filter(
        (article) => !featuredArticleIds.includes(article.id),
    );

    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="bg-white">
                <DiscoverSection articles={articles} />
                <BlogGridSection articles={nonFeaturedArticles} />
            </div>
        </>
    );
};

export default Blogs;
